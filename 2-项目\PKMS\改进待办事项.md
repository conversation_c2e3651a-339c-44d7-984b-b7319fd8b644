# 1. 临时记录

- 如何快速定位曾经的输出成果
- 

# 2. 可行动项

- [ ] 修复drawio插件设置默认样式失效问题
- [ ] 修复`drawio插件`使用快捷键`ctrl+s`保存draw.io后报错：Error: Uncaught TypeError: Cannot set properties of null (setting ‘innerHTML’)问题
- [ ] 优化`drawio插件`预览模式下无法拖动、缩放问题
- [ ] 优化插件`quick-explorer`文件夹文件太多，内容显示过长问题
- [ ] 优化技术债创建脚本（增加元数据自动选择、填充功能）
- [ ] 探索“阻碍”别名的表述中颗粒度的平衡问题
- [ ] 编写自动创建「输出」、自动建立「输出」双链的JS脚本
- [ ] 开发excalidraw自定义脚本（自定义选中内容字体大小） 
- [ ] 开发excalidraw自定义脚本（自定义选中矩形框宽高） 
- [ ] 优化阻碍创建JS脚本（笔记属性自动创建关联阻碍链接） 
- [ ] 优化「dataview-table-style.css」功能（表格宽度自适应） 
- [ ] 为「dashboard」添加“折线图”（添加周维度新增阻碍情况） 

# 3. 待观察项

1. 记录阻碍时，当关联字段的影响对象未创建（不存在）时，应该如何建立链接？
2. 阻碍“relation”字段会伴随项目的推进出现影响范围蔓延的问题，是否需要补充关键链接？
3. 不论阻碍的类型是阻塞型还是价值威胁类型，从影响对象的角度分析，好像都可以关联周计划和周评审？好像都会影响成果交付？
4. 在excallidraw中绘图时，频繁点击鼠标来使用“编辑”功能给工作带了困扰
5. 若发现的阻碍（不处理无法继续工作）在2分钟内可以想到解决方案，但是处理结果和花费的时长不确定，那么这个阻碍还需要记录吗？
6. 遇到阻碍时在2分钟内能够想到解决方案，但是尝试后发现并不能解决问题，此时阻碍应该记录吗？
7. 当前迭代发现成果实现路径错误时，对应观测到的问题应该被作为阻碍记录下来吗？
8. 灵感VS下一步思考VS下一步工作？
9. ”改善代办“与”闪念“管理的方式十分相似，但是发现的BUG或者其他类型的任务应该怎么办
10. 在执行每日任务期间，若当日任务为持续性任务或一日不能完成的任务，那么应该如何记录和安排
11. 在obsidian中新建、重新打开文件或重启软件时，鼠标光标的默认位置会影响文件的查看（特别是开头为代码块），不利于查看和编辑文档
12. 在excallidraw绘图时，默认的字号类型不能覆盖全部的使用场景，导致部分场景下的图形布局杂乱或畸变
13. 在项目推进（迭代）期间，KR进度中已关联的“证据”被优化或删除，原始文件是否需要同时删除？
14. 在进行知识版本管理时，通过技术手段使知识组件笔记属性[update_Date]在修改完文件后自动更新，可以直观了解最新的修改状态
15. 分析`Views`文件夹中文件的代码，提取常用代码块
16. 分析TASK插件预设功能代码，提取常用代码片段
17. 「每周回顾」内容不应长期存在，当改善措施验证通过后，应该取消统计，并将其方案纳入个人效率工具箱中，作为经验沉淀下来